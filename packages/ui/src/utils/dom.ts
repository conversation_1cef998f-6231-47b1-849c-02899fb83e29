import type { ThemeConfig } from "../types/theme";

// Constants for theme class patterns
const THEME_CLASS_PREFIXES = ["theme-", "dark", "light"] as const;

/**
 * Temporarily disables transitions to prevent visual glitches during theme changes
 */
export const withoutTransitions = (callback: () => void): void => {
  if (typeof document === "undefined") return callback();

  const root = document.documentElement;
  root.classList.add("no-transition");

  callback();

  // Re-enable transitions after the DOM has updated
  requestAnimationFrame(() => {
    requestAnimationFrame(() => {
      root.classList.remove("no-transition");
    });
  });
};

/**
 * Initializes smooth theme transitions after page load
 * Call this once when your app initializes to enable smooth transitions
 */
export const initializeThemeTransitions = (): void => {
  if (typeof document === "undefined") return;

  const root = document.documentElement;

  // Remove loading class if it exists
  root.classList.remove("loading");

  // Ensure transitions are enabled
  root.classList.remove("no-transition");
};

/**
 * Determines if the current theme should be dark mode
 */
const shouldUseDarkMode = (config: ThemeConfig): boolean => {
  if (config.mode === "dark") return true;
  if (config.mode === "light") return false;

  // mode === "auto"
  return (
    typeof window !== "undefined" &&
    window.matchMedia("(prefers-color-scheme: dark)").matches
  );
};

/**
 * Removes all existing theme-related classes from the document root
 */
const clearThemeClasses = (root: HTMLElement): void => {
  const existingClasses = Array.from(root.classList).filter((cls) =>
    THEME_CLASS_PREFIXES.some((prefix) => cls.startsWith(prefix))
  );

  if (existingClasses.length > 0) {
    root.classList.remove(...existingClasses);
  }
};

/**
 * Applies the current theme configuration to the document
 */
export function applyTheme(config: ThemeConfig): void {
  if (typeof document === "undefined") return;

  const root = document.documentElement;

  // Check if this is the initial theme application
  const hasExistingTheme =
    root.classList.contains("dark") ||
    root.classList.contains("light") ||
    Array.from(root.classList).some((cls) => cls.startsWith("theme-"));

  const applyThemeClasses = () => {
    // Clear existing theme classes
    clearThemeClasses(root);

    // Apply variant class (skip default variant)
    if (config.variant !== "default") {
      root.classList.add(`theme-${config.variant}`);
    }

    // Apply mode class
    root.classList.add(shouldUseDarkMode(config) ? "dark" : "light");
  };

  // For initial theme application, disable transitions to prevent flash
  // For subsequent changes, allow smooth transitions
  if (!hasExistingTheme) {
    withoutTransitions(applyThemeClasses);
  } else {
    applyThemeClasses();
  }
}
