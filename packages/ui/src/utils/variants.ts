import type { ThemeVariant } from "../types/theme";

// Constants for variant detection
const THEME_VARIANT_REGEX = /\.theme-([a-zA-Z0-9-_]+)/g;
const VALID_VARIANT_NAME = /^[a-zA-Z0-9-_]+$/;

/**
 * Extracts theme variant names from a CSS selector text
 */
const extractVariantsFromSelector = (selectorText: string): string[] => {
  const matches = selectorText.match(THEME_VARIANT_REGEX);
  if (!matches) return [];

  return matches
    .map((match) => match.replace(".theme-", ""))
    .filter((name) => VALID_VARIANT_NAME.test(name));
};

/**
 * Scans a single stylesheet for theme variant classes
 */
const scanStylesheetForVariants = (stylesheet: CSSStyleSheet): string[] => {
  try {
    // Skip external stylesheets to avoid CORS issues
    if (
      stylesheet.href &&
      !stylesheet.href.startsWith(window.location.origin)
    ) {
      return [];
    }

    const rules = stylesheet.cssRules;
    if (!rules) return [];

    const variants: string[] = [];

    for (const rule of Array.from(rules)) {
      if (rule instanceof CSSStyleRule) {
        variants.push(...extractVariantsFromSelector(rule.selectorText));
      }
    }

    return variants;
  } catch {
    // Silently ignore inaccessible stylesheets (CORS, etc.)
    return [];
  }
};

/**
 * Dynamically detects available theme variants from CSS stylesheets
 * by scanning for CSS classes that match the pattern .theme-{name}
 */
export function detectAvailableVariants(): ThemeVariant[] {
  if (typeof window === "undefined" || typeof document === "undefined") {
    return ["default"];
  }

  const variantSet = new Set<ThemeVariant>(["default"]);

  // Scan all stylesheets for theme variants
  for (const stylesheet of Array.from(document.styleSheets)) {
    const variants = scanStylesheetForVariants(stylesheet);
    variants.forEach((variant) => variantSet.add(variant));
  }

  return Array.from(variantSet).sort();
}
