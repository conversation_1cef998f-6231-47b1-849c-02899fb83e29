// Global state for system theme listener
let systemThemeQuery: MediaQueryList | null = null;
let systemThemeListener: (() => void) | null = null;

/**
 * Sets up a listener for system theme preference changes
 */
export function listenToSystemTheme(callback: () => void): void {
  if (typeof window === "undefined") return;

  // Clean up existing listener first
  removeSystemThemeListener();

  systemThemeQuery = window.matchMedia("(prefers-color-scheme: dark)");
  systemThemeListener = callback;
  systemThemeQuery.addEventListener("change", systemThemeListener);
}

/**
 * Removes the system theme preference listener
 */
export function removeSystemThemeListener(): void {
  if (systemThemeQuery && systemThemeListener) {
    systemThemeQuery.removeEventListener("change", systemThemeListener);
    systemThemeQuery = null;
    systemThemeListener = null;
  }
}
