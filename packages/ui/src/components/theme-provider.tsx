import { useMemo, useSyncExternalS<PERSON> } from "react";

import { ThemeContext } from "../context/theme-context";
import { useThemeControl } from "../hooks/use-theme-control";
import { useThemeSync } from "../hooks/use-theme-sync";
import type {
  ThemeConfig,
  ThemeContextValue,
  ThemeVariant,
} from "../types/theme";
import { detectAvailableVariants } from "../utils/variants";

interface ThemeProviderProps {
  children: React.ReactNode;
  storageKey?: string;
  disableStorage?: boolean;
  defaultTheme?: Partial<ThemeConfig>;
  availableVariants?: ThemeVariant[];
}

// Create a reusable media query subscriber
const createMediaQuerySubscriber =
  (query: string) => (subscribe: () => void) => {
    const media = window.matchMedia(query);
    media.addEventListener("change", subscribe);
    return () => media.removeEventListener("change", subscribe);
  };

const darkModeSubscriber = createMediaQuerySubscriber(
  "(prefers-color-scheme: dark)",
);

export function ThemeProvider({
  children,
  storageKey = "theme",
  disableStorage = false,
  defaultTheme = {},
  availableVariants,
}: ThemeProviderProps) {
  // Theme config + actions
  const themeControl = useThemeControl({
    defaultTheme,
    storageKey,
    disableStorage,
  });

  // Side effects: apply theme + listen to system preference
  useThemeSync(themeControl.config, { storageKey, disableStorage });

  // Available theme variants (memoized to prevent unnecessary recalculations)
  const variants = useMemo(
    () => availableVariants ?? detectAvailableVariants(),
    [availableVariants],
  );

  // Computed dark mode state
  const isDark = useSyncExternalStore(
    darkModeSubscriber,
    () => {
      const { mode } = themeControl.config;

      if (mode === "dark") return true;
      if (mode === "light") return false;

      // mode === "auto"
      return (
        typeof window !== "undefined" &&
        window.matchMedia("(prefers-color-scheme: dark)").matches
      );
    },
    // Server-side fallback
    () => false,
  );

  // Memoize context value to prevent unnecessary re-renders
  const contextValue = useMemo<ThemeContextValue>(
    () => ({
      ...themeControl,
      isDark,
      availableVariants: variants,
    }),
    [themeControl, isDark, variants],
  );

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
}
