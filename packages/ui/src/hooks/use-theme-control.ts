import { useCallback, useState } from "react";

import { DEFAULT_CONFIG, THEME_MODES } from "../lib/constants";
import type { ThemeConfig, ThemeMode, ThemeVariant } from "../types/theme";
import { getStoredTheme } from "../utils/presistence";

interface UseThemeControlOptions {
  defaultTheme?: Partial<ThemeConfig>;
  storageKey: string;
  disableStorage: boolean;
}

// Helper function to cycle through array items
const cycleArrayItem = <T>(array: readonly T[], current: T): T => {
  const index = array.indexOf(current);
  return array[(index + 1) % array.length];
};

/**
 * Manages theme state and control actions like mode and variant switching.
 */
export function useThemeControl({
  defaultTheme = {},
  storageKey,
  disableStorage,
}: UseThemeControlOptions) {
  // Initialize config with proper precedence: default < stored < user-provided
  const [config, setConfig] = useState<ThemeConfig>(() => ({
    ...DEFAULT_CONFIG,
    ...(!disableStorage && getStoredTheme(storageKey)),
    ...defaultTheme,
  }));

  // Generic config updater - reduces code duplication
  const updateConfig = useCallback(
    (
      updates:
        | Partial<ThemeConfig>
        | ((prev: ThemeConfig) => Partial<ThemeConfig>),
    ) => {
      setConfig((prev) => ({
        ...prev,
        ...(typeof updates === "function" ? updates(prev) : updates),
      }));
    },
    [],
  );

  const setMode = useCallback(
    (mode: ThemeMode) => updateConfig({ mode }),
    [updateConfig],
  );

  const toggleMode = useCallback(
    () =>
      updateConfig((prev) => ({
        mode: cycleArrayItem(THEME_MODES, prev.mode),
      })),
    [updateConfig],
  );

  const setVariant = useCallback(
    (variant: ThemeVariant) => updateConfig({ variant }),
    [updateConfig],
  );

  const cycleVariant = useCallback(
    (availableVariants: ThemeVariant[]) =>
      updateConfig((prev) => ({
        variant: cycleArrayItem(availableVariants, prev.variant),
      })),
    [updateConfig],
  );

  return {
    config,
    setConfig,
    setMode,
    toggleMode,
    setVariant,
    cycleVariant,
  } as const;
}
