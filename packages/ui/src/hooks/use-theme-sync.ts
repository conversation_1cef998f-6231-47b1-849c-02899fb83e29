import { useEffect } from "react";

import type { ThemeConfig } from "../types/theme";
import { applyTheme } from "../utils/dom";
import { persistTheme } from "../utils/presistence";
import {
  listenToSystemTheme,
  removeSystemThemeListener,
} from "../utils/system";

interface UseThemeSyncOptions {
  storageKey: string;
  disableStorage: boolean;
}

/**
 * Syncs theme changes to DOM, storage, and system preferences.
 */
export function useThemeSync(
  config: ThemeConfig,
  { storageKey, disableStorage }: UseThemeSyncOptions,
) {
  // Apply theme to DOM and persist to storage
  useEffect(() => {
    applyTheme(config);

    // Only persist if storage is enabled
    if (!disableStorage) {
      persistTheme(config, storageKey);
    }
  }, [config, storageKey, disableStorage]);

  // Listen to system theme changes when in auto mode
  useEffect(() => {
    // Only listen to system theme changes in auto mode
    if (config.mode !== "auto") return;

    // Re-apply theme when system preference changes
    const handleSystemThemeChange = () => applyTheme(config);

    listenToSystemTheme(handleSystemThemeChange);

    // Cleanup listener on unmount or when mode changes
    return removeSystemThemeListener;
  }, [config.mode, config.variant]); // Only depend on relevant config parts
}
